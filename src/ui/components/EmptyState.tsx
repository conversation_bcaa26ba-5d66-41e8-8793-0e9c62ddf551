import { Box } from './Box';
import { Icon, IconName } from './Icon';
import { Text } from './Text';

export interface EmptyStateProps {
  /** Ícone a ser exibido */
  icon: IconName;
  /** <PERSON><PERSON><PERSON>lo principal */
  title: string;
  /** Descrição opcional */
  description?: string;
  /** Tamanho do ícone */
  iconSize?: number;
  /** Cor do ícone */
  iconColor?: string;
  /** Espaçamento superior */
  marginTop?: string;
}

export function EmptyState({
  icon,
  title,
  description,
  iconSize = 32,
  iconColor = "gray500",
  marginTop = "s56"
}: EmptyStateProps) {
  return (
    <Box 
      alignItems="center" 
      gap="s16" 
      marginTop={marginTop} 
      justifyContent="center" 
      paddingHorizontal="s32"
    >
      <Box 
        width={80} 
        height={80} 
        borderRadius="rounded" 
        backgroundColor="gray50" 
        alignItems="center" 
        justifyContent="center"
        marginBottom="s8"
      >
        <Icon name={icon} color={iconColor} size={iconSize} />
      </Box>
      
      <Text variant="text16Medium" color="gray900" textAlign="center">
        {title}
      </Text>
      
      {description && (
        <Text 
          variant="text14Regular" 
          color="gray500" 
          textAlign="center" 
          lineHeight={20}
        >
          {description}
        </Text>
      )}
    </Box>
  );
}
