# GradientIconButton

Um componente de botão reutilizável com gradiente linear e ícone tipado, perfeito para criar botões elegantes e consistentes em todo o aplicativo.

## Características

- ✅ **Gradiente linear** com as cores #535353 e #000000 (customizável)
- ✅ **Tipagem completa** para ícones usando `IconName`
- ✅ **Múltiplos layouts**: circular, retangular, com/sem texto
- ✅ **Estado de carregamento** integrado
- ✅ **Totalmente customizável** (tamanhos, cores, estilos)
- ✅ **Acessibilidade** e props do TouchableOpacity

## Importação

```tsx
import { GradientIconButton } from '@/src/ui/components';
```

## Props

| Prop | Tipo | Padrão | Descrição |
|------|------|--------|-----------|
| `iconName` | `IconName` | - | **Obrigatório.** Nome do ícone a ser exibido |
| `onPress` | `() => void` | - | **Obrigatório.** Função chamada ao pressionar |
| `title` | `string` | - | Texto do botão (opcional) |
| `iconSize` | `number` | `24` | Tamanho do ícone em pixels |
| `size` | `number` | `56` | Tamanho do botão em pixels |
| `gradientColors` | `string[]` | `['#535353', '#000000']` | Cores do gradiente |
| `isLoading` | `boolean` | `false` | Estado de carregamento |
| `iconOnly` | `boolean` | `false` | Se deve mostrar apenas o ícone |
| `textProps` | `TextProps` | - | Props adicionais para o texto |
| `activeOpacity` | `number` | `0.8` | Opacidade ao pressionar |

## Exemplos de Uso

### Botão Circular Simples

```tsx
<GradientIconButton
  iconName="pix"
  onPress={() => console.log('PIX pressed')}
  iconOnly
/>
```

### Botão com Texto Abaixo

```tsx
<GradientIconButton
  iconName="transfer"
  title="Transferir"
  onPress={() => handleTransfer()}
  iconOnly
/>
```

### Botão Retangular com Texto ao Lado

```tsx
<GradientIconButton
  iconName="support"
  title="Suporte"
  onPress={() => openSupport()}
  size={120}
  iconOnly={false}
/>
```

### Botão com Gradiente Customizado

```tsx
<GradientIconButton
  iconName="wallet"
  title="Carteira"
  onPress={() => openWallet()}
  gradientColors={['#FF6B6B', '#FF8E53']}
  iconOnly
/>
```

### Botão em Estado de Carregamento

```tsx
<GradientIconButton
  iconName="wallet"
  title="Processando..."
  onPress={() => {}}
  isLoading={true}
  iconOnly
/>
```

### Diferentes Tamanhos

```tsx
{/* Pequeno */}
<GradientIconButton
  iconName="bell"
  onPress={() => {}}
  size={40}
  iconSize={18}
  iconOnly
/>

{/* Médio (padrão) */}
<GradientIconButton
  iconName="bell"
  onPress={() => {}}
  size={56}
  iconSize={24}
  iconOnly
/>

{/* Grande */}
<GradientIconButton
  iconName="bell"
  onPress={() => {}}
  size={72}
  iconSize={30}
  iconOnly
/>
```

## Ícones Disponíveis

O componente usa a tipagem `IconName` que inclui todos os ícones disponíveis:

- `home`, `wallet`, `sideBar`, `search`, `chevronLeft`
- `bell`, `moneyRecive`, `pix`, `receiveMoney`, `card`
- `support`, `walletCheck`, `qrCode`, `copyPaste`

## Integração com o Sistema de Design

O componente segue os padrões do sistema de design do projeto:

- **Cores**: Usa o tema de cores definido em `theme.ts`
- **Tipografia**: Usa as variantes de texto do tema
- **Espaçamento**: Usa os tokens de espaçamento do tema
- **Ícones**: Integrado com o sistema de ícones existente

## Acessibilidade

O componente herda todas as props de acessibilidade do `TouchableOpacity`, incluindo:

- `accessibilityLabel`
- `accessibilityHint`
- `accessibilityRole`
- `testID`

```tsx
<GradientIconButton
  iconName="pix"
  onPress={() => {}}
  accessibilityLabel="Botão PIX"
  accessibilityHint="Abre a área PIX"
  testID="pix-button"
  iconOnly
/>
```
