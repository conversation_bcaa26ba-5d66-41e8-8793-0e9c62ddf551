import LinearChart from '../../assets/svg/linear-chart.svg';
import { Box, BoxProps } from "./Box";
import { GradientBox } from "./GradientBox";
import { Icon, IconName } from "./Icon";
import { Text, TextProps } from "./Text";
import { GradientIconButton } from './GradientIconButton';

type CardWrapperProps = {
  boxProps?: BoxProps;
  preset?: CardPreset;
  icon?: IconName | null;
  sizeWrapperIcon?: number;
  sizeIcon?: number;
  label: string;
  value: string;

}

export type CardPreset = 'primary' | 'outline';

interface CardWrapperUI {
  container: BoxProps;
  label: TextProps;
  value: TextProps;
}

export const cardPresets: Record<
  CardPreset,
  {
    default: CardWrapperUI;
  }
> = {
  primary: {
    default: {
      container: {
        backgroundColor: 'midnightBlack',
        gap: 's8',
      },
      label: {
        color: 'white',
        fontFamily: 'text24Bold',
        fontSize: 16,
        lineHeight: 18,
      },
      value: {
        marginTop: 's8',
        color: 'white',
        fontFamily: 'DMSansSemiBold',
        fontSize: 25,
        lineHeight: 27,
      },
    },
  },
  outline: {
    default: {
      container: {
        backgroundColor: 'white',
        paddingHorizontal: 's16',
        gap: 's16',
      },
      label: {
        color: 'gray600',
        fontFamily: 'DMSansMedium',
        fontSize: 12 ,
        lineHeight: 14,
      },
      value: {
        color: 'midnightBlack',
        fontFamily: 'DMSansSemiBold',
        fontSize: 17,
        lineHeight: 19,
      },
    },
  },
};

export function CardBalance({
  boxProps,
  preset = 'primary',
  label,
  icon,
  sizeWrapperIcon = 56,
  sizeIcon = 24,
  value,
}: CardWrapperProps) {
  const cardPreset = cardPresets[preset]['default'];

  // Se for o preset primary, usar GradientBox, senão usar Box normal
  if (preset === 'primary') {
    return (
      <Box
        borderRadius="secondary"
        overflow="hidden"
        {...boxProps}
      >
        <GradientBox colors={['#535353', '#000000']}>
          <Box
            paddingVertical="s24"
            paddingHorizontal="s16"
            gap="s8"
            position="relative"
          >
            <Box flexDirection="row" flex={1} gap="s8" alignItems="center">
              {Boolean(icon) && <Icon name={icon as IconName} color={cardPreset.label.color} size={sizeIcon} />}
              <Text {...cardPreset.label} flexShrink={1} variant="text12Light">{label}</Text>
            </Box>
            <Text {...cardPreset.value} variant="text24Bold">{value}</Text>

            {/* LinearChart no canto direito */}
            <Box position="absolute" top={20} right={16}>
              <LinearChart width={80} height={50} />
            </Box>
          </Box>
        </GradientBox>
      </Box>
    );
  }

  return (
    <Box
      borderRadius="secondary"
      paddingVertical="s20"
      paddingHorizontal="s16"
      backgroundColor="white"
      {...cardPreset.container}
      {...boxProps}
    >
      <Box flexDirection="row"  flex={1} gap="s14" alignItems="center">
        {Boolean(icon) &&
         <GradientIconButton
          iconName={icon as IconName}
          iconSize={sizeIcon}
          onPress={() => {}}
          size={sizeWrapperIcon}
          gradientColors={['#535353', '#000000']}
         />
        }
        <Box gap="s8">
          <Text {...cardPreset.label} flexShrink={1} fontSize={12} lineHeight={14} fontFamily="DMSansMedium">{label}</Text>
          <Text {...cardPreset.value} variant="text16Medium">{value}</Text>
        </Box>
      </Box>
    </Box>
  );
}