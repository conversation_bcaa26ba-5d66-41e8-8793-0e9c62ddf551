import { useRouter } from "expo-router";
import { Box, TouchableOpacityBox } from "./Box";
import { Icon, IconName } from "./Icon";
import { Text } from "./Text";
import { useAppTheme } from "../theme/useAppTheme";

export interface NavigationOption {
  type: OptionNavigateProps;
}
  
export type OptionNavigateProps = "pix" | "wallet" | "transactions" | "support" | "transfer" | "QRCode" | "copyandpaste"

const cards: Record<OptionNavigateProps, {
  label: string, route: string
 }> = {
  ['pix']: {
    label: 'Área Pix',
    route: '/(protected)/(area-pix)',
  },
  ['wallet']: {
    label: 'Carteira',
    route: '/(protected)/(tabs)/wallet',
  },
  ['transactions']: {
    label: 'Transações',
    route: '/(protected)/(tabs)/transaction-history',
  },
  ['support']: {
    label: 'Suporte',
    route: '/support',
  },
  ['transfer']: {
    label: 'Transferir',
    route: '/(protected)/(area-pix)/send-pix',
  },
  ['QRCode']: {
    label: 'Ler QR CODE',
    route: '/(protected)/(area-pix)/send-pix',
  },
  ['copyandpaste']: {
    label: `Pix Copiar e${'\n'}Colar`,
    route: '/copyandpaste',
  },
}

interface Props {
  type: OptionNavigateProps;
  width?: number;
  fontSize?: 'small' | 'medium';
}

export function OptionNavigate({ type, width = 78, fontSize = 'small' }: Props) {
  const router = useRouter();
  const { boxShadows } = useAppTheme()
  const options: Record<string, IconName> = {
    pix: "pix",
    wallet: "wallet",
    transactions: "moneyRecive",
    support: "support",
    transfer: "receiveMoney",
    QRCode: "qrCode",
    copyandpaste: "copyPaste",
  };

  const card = cards[type]

  return (
    <Box 
      gap="s10"
    >
      <TouchableOpacityBox
        backgroundColor="white"
        borderRadius="secondary"
        padding="s24"
        alignItems="center"
        width= {width}
        justifyContent="center"
        activeOpacity={0.8}
        onPress={() => router.push(card.route as any)}
        style={{boxShadow: boxShadows.primary}}
      >
        <Icon name={options[type]} color="black" size={fontSize === 'small' ? 22 : 28} />
      </TouchableOpacityBox>
      <Text textAlign="center" flexShrink={1} variant={fontSize === 'small' ? 'text10Medium' : 'text12Medium'}>{card.label}</Text>
    </Box>
  );
}