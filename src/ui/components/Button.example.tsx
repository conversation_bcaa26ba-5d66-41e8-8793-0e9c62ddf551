import React from 'react';
import { Box } from './Box';
import { But<PERSON> } from './Button';
import { Text } from './Text';

/**
 * Exemplo de uso do componente Button com preset gradient
 * 
 * Este arquivo demonstra como usar o novo preset 'gradient' 
 * que aplica um gradiente linear com as cores #535353 e #000000
 */
export function ButtonGradientExample() {
  return (
    <Box padding="s24" gap="s24">
      <Text variant="text18Medium" textAlign="center">
        Exemplos do Button com Gradient
      </Text>

      {/* Botão com preset gradient */}
      <Box gap="s12">
        <Text variant="text14Medium">Botão com gradiente:</Text>
        <Button
          variant="gradient"
          title="Continuar"
          onPress={() => console.log('Gradient button pressed')}
        />
      </Box>

      {/* Botão com gradiente e ícone */}
      <Box gap="s12">
        <Text variant="text14Medium">Botão com gradiente e ícone:</Text>
        <Button
          variant="gradient"
          title="Transferir"
          icon="receiveMoney"
          onPress={() => console.log('Gradient button with icon pressed')}
        />
      </Box>

      {/* Botão com gradiente em estado de carregamento */}
      <Box gap="s12">
        <Text variant="text14Medium">Botão com gradiente carregando:</Text>
        <Button
          variant="gradient"
          title="Processando..."
          onPress={() => console.log('Loading button pressed')}
          isLoading={true}
        />
      </Box>

      {/* Comparação com outros presets */}
      <Box gap="s12">
        <Text variant="text14Medium">Comparação com outros presets:</Text>
        
        <Button
          variant="light"
          title="Botão Light"
          onPress={() => console.log('Light button pressed')}
        />
        
        <Button
          variant="dark"
          title="Botão Dark"
          onPress={() => console.log('Dark button pressed')}
        />
        
        <Button
          variant="gradient"
          title="Botão Gradient"
          onPress={() => console.log('Gradient button pressed')}
        />
      </Box>

      {/* Botão com gradiente e props customizadas */}
      <Box gap="s12">
        <Text variant="text14Medium">Botão com props customizadas:</Text>
        <Button
          variant="gradient"
          title="Botão Customizado"
          icon="pix"
          onPress={() => console.log('Custom gradient button pressed')}
          textProps={{
            variant: "text14SemiBold"
          }}
        />
      </Box>
    </Box>
  );
}
