import React from 'react';
import { Box } from './Box';
import { EmptyState } from './EmptyState';
import { Text } from './Text';

/**
 * Exemplo de uso do componente EmptyState
 * 
 * Este arquivo demonstra as diferentes formas de usar o componente
 * EmptyState para exibir estados vazios em listas.
 */
export function EmptyStateExample() {
  return (
    <Box padding="s24" gap="s32">
      <Text variant="text18Medium" textAlign="center">
        Exemplos do EmptyState
      </Text>

      {/* Estado vazio para transações */}
      <Box gap="s12">
        <Text variant="text14Medium">Para lista de transações:</Text>
        <Box backgroundColor="white" borderRadius="secondary" padding="s16">
          <EmptyState
            icon="moneyRecive"
            title="Nenhuma transação ainda"
            description="Suas últimas transações aparecerão aqui quando você começar a usar o app"
          />
        </Box>
      </Box>

      {/* Estado vazio para PIX */}
      <Box gap="s12">
        <Text variant="text14Medium">Para área PIX:</Text>
        <Box backgroundColor="white" borderRadius="secondary" padding="s16">
          <EmptyState
            icon="pix"
            title="Nenhuma transação PIX ainda"
            description="Suas transações PIX aparecerão aqui quando você começar a usar"
          />
        </Box>
      </Box>

      {/* Estado vazio para carteira */}
      <Box gap="s12">
        <Text variant="text14Medium">Para carteira:</Text>
        <Box backgroundColor="white" borderRadius="secondary" padding="s16">
          <EmptyState
            icon="wallet"
            title="Carteira vazia"
            description="Adicione fundos à sua carteira para começar a usar"
          />
        </Box>
      </Box>

      {/* Estado vazio customizado */}
      <Box gap="s12">
        <Text variant="text14Medium">Customizado (sem descrição):</Text>
        <Box backgroundColor="white" borderRadius="secondary" padding="s16">
          <EmptyState
            icon="search"
            title="Nenhum resultado encontrado"
            iconSize={28}
            iconColor="blue"
            marginTop="s32"
          />
        </Box>
      </Box>

      {/* Estado vazio para notificações */}
      <Box gap="s12">
        <Text variant="text14Medium">Para notificações:</Text>
        <Box backgroundColor="white" borderRadius="secondary" padding="s16">
          <EmptyState
            icon="bell"
            title="Nenhuma notificação"
            description="Você receberá notificações importantes aqui"
          />
        </Box>
      </Box>
    </Box>
  );
}
