import React from 'react';
import { Pressable } from 'react-native';

import { ThemeColors } from '@/src/ui/theme/theme';
import { useAppTheme } from '@/src/ui/theme/useAppTheme';

//import { WalletCheckIcon } from '@/src/assets/icons/WalletCheckIcon';
import { BellIcon } from '@/src/assets/icons/BellIcon';
import { ChevronLeftIcon } from '@/src/assets/icons/ChevronLeftIcon';
import { HomeIcon } from '@/src/assets/icons/HomeIcon';
import { MoneyReciveIcon } from '@/src/assets/icons/MoneyReciveIcon';
import { PixIcon } from '@/src/assets/icons/PixIcon';
import { SearchIcon } from '@/src/assets/icons/SearchIcon';
import { SideBarIcon } from '@/src/assets/icons/SideBarIcon';
import { WalletIcon } from '@/src/assets/icons/WalletIcon';
import { ReceiveMoneyIcon } from '@/src/assets/icons/ReceiveMoneyIcon';
import { CardIcon } from '@/src/assets/icons/CardIcon';
import { SupportIcon } from '@/src/assets/icons/SuportIcon';
import { WalletCheckIcon } from '@/src/assets/icons/WalletCheckIcon';
import { QrCodeIcon } from '@/src/assets/icons/QrCodeIcon';
import { CopyPasteIcon } from '@/src/assets/icons/CopyPasteIcon';
import { ArrowRightIcon } from '@/src/assets/icons/ArrowRightIcon';
import { EditIcon } from '@/src/assets/icons/EditIcon';

export interface IconBase {
  size?: number;
  color?: string;
  fillColor?: string;
}
export interface IconProps {
  name: IconName;
  color?: ThemeColors;
  fillColor?: ThemeColors;
  size?: number;
  onPress?: () => void;
}

export function Icon({
  name,
  color = 'text',
  fillColor = 'text',
  size,
  onPress,
}: IconProps) {
  const {colors} = useAppTheme();
  const SVGIcon = iconRegistry[name];

  const iconProps: React.ComponentProps<typeof SVGIcon> = {
    size,
    color: colors[color],
    fillColor: colors[fillColor],
  };

  if (onPress) {
    return (
      <Pressable testID={name} hitSlop={10} onPress={onPress}>
        <SVGIcon {...iconProps} />
      </Pressable>
    );
  }

  return <SVGIcon {...iconProps} />;
}

const iconRegistry = {
  home: HomeIcon,
  wallet: WalletIcon,
  sideBar: SideBarIcon,
  search: SearchIcon,
  chevronLeft: ChevronLeftIcon,
  bell: BellIcon,
  moneyRecive: MoneyReciveIcon,
  pix: PixIcon,
  receiveMoney: ReceiveMoneyIcon,
  card: CardIcon,
  support: SupportIcon,
  walletCheck: WalletCheckIcon,
  qrCode: QrCodeIcon,
  copyPaste: CopyPasteIcon,
  arrowRight: ArrowRightIcon,
  edit: EditIcon,
};

export type IconType = typeof iconRegistry;

export type IconName = keyof IconType;

