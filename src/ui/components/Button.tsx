import { LinearGradient } from 'expo-linear-gradient';
import { ActivityIndicator, TouchableOpacity } from "react-native";
import { useAppTheme } from "../theme/useAppTheme";
import { Box, BoxProps, TouchableOpacityBox, TouchableOpacityBoxProps } from "./Box";
import { Icon, IconName } from "./Icon";
import { Text, TextProps } from "./Text";

export type ButtonPreset = 'light' | 'dark' | 'gradient';

interface ButtonWrapperUI {
  container: BoxProps;
  label: TextProps;
}

export const buttonPresets: Record<
  ButtonPreset,
  {
    default: ButtonWrapperUI;
  }
> = {
  light: {
    default: {
      container: {
        backgroundColor: 'white',

      },
      label: {
        color: 'midnightBlack',
        variant: "text16Medium",
      },
    },
  },
  dark: {
    default: {
      container: {
        borderWidth: 0,
        borderColor: 'border',
        backgroundColor: 'midnightBlack',
        gap: 's8',
      },
      label: {
        color: 'white',
        variant: "text16Medium",
        lineHeight: 0,
      },
    },
  },
  gradient: {
    default: {
      container: {
        borderWidth: 0,
        borderColor: 'transparent',
        backgroundColor: 'transparent',
        gap: 's8',
      },
      label: {
        color: 'white',
        variant: "text16Medium",
        lineHeight: 0,
      },
    },
  },
};

type ButtonProps = TouchableOpacityBoxProps & {
  title: string;
  icon?: IconName | null;
  onPress: () => void;
  variant?: ButtonPreset;
  isLoading?: boolean;
  boxProps?: BoxProps;
  textProps?: TextProps;
};

export function Button({
  title,
  icon = null,
  onPress,
  variant = "dark",
  isLoading = false,
  boxProps,
  textProps,
  ...toProps
}: ButtonProps) {
  const buttonProps = buttonPresets[variant]['default'];
  const { borderRadii } = useAppTheme();

  // Mapear borderRadius do tema
  const getBorderRadius = () => {
    if (boxProps?.borderRadius && typeof boxProps.borderRadius === 'string') {
      return borderRadii[boxProps.borderRadius as keyof typeof borderRadii] || 16;
    }
    return 16;
  };

  // Se for o preset gradient, usar LinearGradient
  if (variant === 'gradient') {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.8}
        style={{
          borderRadius: getBorderRadius(),
          overflow: 'hidden',
          width: boxProps?.width || '100%',
        }}
        {...toProps}
      >
        <LinearGradient
          colors={['#535353', '#000000']}
          start={[0, 0]}
          end={[1, 1]}
          style={{
            padding: 16,
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
            gap: 8,
          }}
        >
          {isLoading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <>
              {Boolean(icon) && <Icon name={icon as IconName} color="white" size={24} />}
              <Text
                color="white"
                flexShrink={1}
                variant="text16Medium"
                {...textProps}
              >
                {title}
              </Text>
            </>
          )}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  // Para outros presets, usar o componente normal
  return (
    <TouchableOpacityBox
      {...toProps}
      onPress={onPress}
      backgroundColor={buttonProps.container.backgroundColor}
      borderRadius="default"
      activeOpacity={0.8}
      padding="s16"
      width="100%"
      flexShrink={1}
       flex={1}
      justifyContent="center"
      alignItems="center"
      {...boxProps}
    >
      {isLoading ? (
        <ActivityIndicator color={buttonProps.label.color} size="small" />
      ) : (
        <Box gap="s8" flexDirection="row" alignItems="center">
          {Boolean(icon) && <Icon name={icon as IconName} color={buttonProps.label.color} size={24} />}
          <Text
            color={buttonProps.label.color}
            flexShrink={1}
            variant={buttonProps.label.variant}
            {...textProps}
          >
            {title}
          </Text>
        </Box>
      )}
    </TouchableOpacityBox>
  );
}