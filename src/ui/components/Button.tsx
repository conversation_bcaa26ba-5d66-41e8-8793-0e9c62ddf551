import { ActivityIndicator } from "react-native";
import { ThemeColors } from "../theme/theme";
import { Box, BoxProps, TouchableOpacityBox, TouchableOpacityBoxProps } from "./Box";
import { Text, TextProps } from "./Text";
import { Icon, IconName, IconProps } from "./Icon";

export type ButtonPreset = 'light' | 'dark';

interface ButtonWrapperUI {
  container: BoxProps;
  label: TextProps;
}

export const buttonPresets: Record<
  ButtonPreset,
  {
    default: ButtonWrapperUI;
  }
> = {
  light: {
    default: {
      container: {
        backgroundColor: 'white',

      },
      label: {
        color: 'midnightBlack',
        variant: "text16Medium",
      },
    },
  },
  dark: {
    default: {
      container: {
        borderWidth: 0,
        borderColor: 'border',
        backgroundColor: 'midnightBlack',
        gap: 's8',
      },
      label: {
        color: 'white',
        variant: "text16Medium",
        lineHeight: 0,
      },
    },
  },
};

type ButtonProps = TouchableOpacityBoxProps & {
  title: string;
  icon?: IconName | null;
  onPress: () => void;
  variant?: ButtonPreset;
  isLoading?: boolean;
  boxProps?: BoxProps;
  textProps?: TextProps;
};

export function Button({
  title,
  icon = null,
  onPress,
  variant = "dark",
  isLoading = false,
  boxProps,
  textProps,
  ...toProps
}: ButtonProps) {
  const buttonProps = buttonPresets[variant]['default'];
  return (
    <TouchableOpacityBox
      {...toProps}
      onPress={onPress}
      backgroundColor={buttonProps.container.backgroundColor}
      borderRadius="default"
      activeOpacity={0.8}
      padding="s16"
      width="100%"
      flexShrink={1}
       flex={1}
      justifyContent="center"
      alignItems="center"
      {...boxProps}
    >
      {isLoading ? (
        <ActivityIndicator color={buttonProps.label.color} size="small" />
      ) : (
        <Box gap="s8" flexDirection="row" alignItems="center">
          {Boolean(icon) && <Icon name={icon as IconName} color={buttonProps.label.color} size={24} />}
          <Text
            color={buttonProps.label.color}
            flexShrink={1}
            variant={buttonProps.label.variant}
            {...textProps}
          >
            {title}
          </Text>
        </Box>
      )}
    </TouchableOpacityBox>
  );
}