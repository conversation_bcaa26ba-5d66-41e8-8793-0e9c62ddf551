# EmptyState

Um componente reutilizável para exibir estados vazios em listas e outras áreas do aplicativo de forma consistente e atrativa.

## Características

- ✅ **Design consistente** com ícone, título e descrição
- ✅ **Totalmente customizável** (ícone, cores, tamanhos, espaçamentos)
- ✅ **Tipagem completa** para ícones usando `IconName`
- ✅ **Responsivo** e adaptável a diferentes contextos
- ✅ **Fácil de usar** com props intuitivas

## Importação

```tsx
import { EmptyState } from '@/src/ui/components';
```

## Props

| Prop | Tipo | Padrão | Descrição |
|------|------|--------|-----------|
| `icon` | `IconName` | - | **Obrigatório.** Ícone a ser exibido |
| `title` | `string` | - | **Obrigatório.** T<PERSON><PERSON>lo principal |
| `description` | `string` | - | Descrição opcional |
| `iconSize` | `number` | `32` | Tamanho do ícone em pixels |
| `iconColor` | `string` | `"gray500"` | Cor do ícone |
| `marginTop` | `string` | `"s56"` | Espaçamento superior |

## Exemplos de Uso

### Uso Básico

```tsx
<EmptyState
  icon="moneyRecive"
  title="Nenhuma transação ainda"
  description="Suas últimas transações aparecerão aqui quando você começar a usar o app"
/>
```

### Em FlatList

```tsx
<FlatList
  data={transactions}
  renderItem={({ item }) => <TransactionCard data={item} />}
  ListEmptyComponent={() => (
    <EmptyState
      icon="moneyRecive"
      title="Nenhuma transação ainda"
      description="Suas últimas transações aparecerão aqui quando você começar a usar o app"
    />
  )}
/>
```

### Sem Descrição

```tsx
<EmptyState
  icon="search"
  title="Nenhum resultado encontrado"
/>
```

### Customizado

```tsx
<EmptyState
  icon="wallet"
  title="Carteira vazia"
  description="Adicione fundos à sua carteira para começar a usar"
  iconSize={28}
  iconColor="blue"
  marginTop="s32"
/>
```

## Casos de Uso Comuns

### Transações

```tsx
<EmptyState
  icon="moneyRecive"
  title="Nenhuma transação ainda"
  description="Suas últimas transações aparecerão aqui quando você começar a usar o app"
/>
```

### PIX

```tsx
<EmptyState
  icon="pix"
  title="Nenhuma transação PIX ainda"
  description="Suas transações PIX aparecerão aqui quando você começar a usar"
/>
```

### Busca

```tsx
<EmptyState
  icon="search"
  title="Nenhum resultado encontrado"
  description="Tente usar palavras-chave diferentes"
/>
```

### Notificações

```tsx
<EmptyState
  icon="bell"
  title="Nenhuma notificação"
  description="Você receberá notificações importantes aqui"
/>
```

### Carteira

```tsx
<EmptyState
  icon="wallet"
  title="Carteira vazia"
  description="Adicione fundos à sua carteira para começar a usar"
/>
```

## Estrutura Visual

O componente segue uma estrutura visual consistente:

```
┌─────────────────────────────────┐
│                                 │
│         ┌─────────────┐         │
│         │             │         │
│         │    ÍCONE    │         │
│         │             │         │
│         └─────────────┘         │
│                                 │
│           TÍTULO                │
│                                 │
│         Descrição               │
│         (opcional)              │
│                                 │
└─────────────────────────────────┘
```

## Especificações de Design

### Container do Ícone
- **Tamanho**: 80x80px
- **Background**: gray50
- **Border radius**: rounded
- **Alinhamento**: center

### Tipografia
- **Título**: text16Medium, gray900
- **Descrição**: text14Regular, gray500, lineHeight: 20

### Espaçamentos
- **Gap entre elementos**: s16
- **Margin bottom do ícone**: s8
- **Padding horizontal**: s32
- **Margin top padrão**: s56

## Integração com o Sistema de Design

O componente está totalmente integrado com o sistema de design:

- **Cores**: Usa tokens de cor do tema
- **Tipografia**: Usa variantes de texto do tema
- **Espaçamento**: Usa tokens de espaçamento do tema
- **Ícones**: Integrado com o sistema de ícones existente

## Acessibilidade

- Texto centralizado para melhor legibilidade
- Contraste adequado entre texto e fundo
- Estrutura semântica clara
- Suporte a diferentes tamanhos de tela

## Vantagens

- **Consistência**: Todos os estados vazios têm a mesma aparência
- **Manutenibilidade**: Mudanças no design afetam todos os usos
- **Produtividade**: Rápido de implementar em novas telas
- **Flexibilidade**: Customizável para diferentes contextos
