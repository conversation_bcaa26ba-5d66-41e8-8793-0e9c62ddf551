# Button - Preset Gradient

O componente `Button` agora suporta um novo preset chamado `gradient` que aplica um gradiente linear com as cores **#535353** e **#000000**.

## Características do Preset Gradient

- ✅ **Gradiente linear** com as cores #535353 (cinza médio) e #000000 (preto)
- ✅ **Texto branco** para contraste adequado
- ✅ **Suporte a ícones** com cor branca
- ✅ **Estado de carregamento** integrado
- ✅ **Bo<PERSON><PERSON> arredonda<PERSON>** (16px)
- ✅ **Efeito de pressão** com `activeOpacity={0.8}`

## Como usar

### Importação

```tsx
import { Button } from '@/src/ui/components';
```

### Uso básico

```tsx
<Button
  variant="gradient"
  title="Continuar"
  onPress={() => console.log('Pressed')}
/>
```

### Com ícone

```tsx
<Button
  variant="gradient"
  title="Transferir"
  icon="receiveMoney"
  onPress={() => handleTransfer()}
/>
```

### Em estado de carregamento

```tsx
<Button
  variant="gradient"
  title="Processando..."
  onPress={() => {}}
  isLoading={true}
/>
```

### Com props customizadas

```tsx
<Button
  variant="gradient"
  title="Botão Customizado"
  icon="pix"
  onPress={() => {}}
  textProps={{
    variant: "text14SemiBold"
  }}
/>
```

## Especificações Técnicas

### Cores do Gradiente
- **Cor inicial**: #535353 (cinza médio)
- **Cor final**: #000000 (preto)
- **Direção**: Diagonal (start: [0, 0], end: [1, 1])

### Estilo
- **Padding**: 16px
- **Border radius**: 16px
- **Texto**: Branco (#FFFFFF)
- **Ícone**: Branco (#FFFFFF)
- **Tamanho do ícone**: 24px

### Layout
- **Direção**: Row (ícone + texto lado a lado)
- **Alinhamento**: Center
- **Justificação**: Center
- **Gap**: 8px entre ícone e texto

## Comparação com outros presets

| Preset | Background | Texto | Uso recomendado |
|--------|------------|-------|-----------------|
| `light` | Branco | Preto | Ações secundárias |
| `dark` | Preto sólido | Branco | Ações primárias |
| `gradient` | Gradiente cinza-preto | Branco | Ações de destaque |

## Quando usar o preset gradient

- ✅ **Ações principais** que precisam de destaque visual
- ✅ **Call-to-actions** importantes
- ✅ **Botões de confirmação** em fluxos críticos
- ✅ **Ações de transferência** ou transações financeiras
- ✅ **Botões que precisam se destacar** na interface

## Acessibilidade

O preset gradient mantém todas as características de acessibilidade do componente Button:

- Contraste adequado (texto branco sobre fundo escuro)
- Suporte a `accessibilityLabel`
- Suporte a `accessibilityHint`
- Área de toque adequada
- Estados visuais claros (normal, pressionado, carregando)

## Exemplo completo

```tsx
import React from 'react';
import { Button } from '@/src/ui/components';

export function TransferScreen() {
  const handleTransfer = () => {
    console.log('Iniciando transferência...');
  };

  return (
    <Button
      variant="gradient"
      title="Confirmar Transferência"
      icon="receiveMoney"
      onPress={handleTransfer}
      accessibilityLabel="Confirmar transferência"
      accessibilityHint="Toque para confirmar a transferência"
    />
  );
}
```
