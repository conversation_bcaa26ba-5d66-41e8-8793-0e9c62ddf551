import { PropsWithChildren } from "react";
import { KeyboardAvoidingView, Platform, SafeAreaView, ScrollView, TouchableWithoutFeedback, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Box, BoxProps } from "./Box";
import { useAppTheme } from "../theme/useAppTheme";

export function Screen({
  children,
  customHeader,
  scrollable = false,
  ...boxProps
}: PropsWithChildren & BoxProps & { scrollable?: boolean, customHeader?: React.ReactNode }) {
  const Container = scrollable ? ScrollView : View;
  const { colors } = useAppTheme();
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : 'height'}
      >
        {Boolean(customHeader) && customHeader}
        <Box
          flex={1}
          backgroundColor="background"
          paddingHorizontal="s24"
          {...boxProps}
        >
          <Container
            showsVerticalScrollIndicator={false}>{children}
          </Container>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}