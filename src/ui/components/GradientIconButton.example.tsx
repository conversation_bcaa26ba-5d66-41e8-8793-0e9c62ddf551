import React from 'react';
import { Box } from './Box';
import { GradientIconButton } from './GradientIconButton';
import { Text } from './Text';

/**
 * Exemplo de uso do componente GradientIconButton
 * 
 * Este arquivo demonstra as diferentes formas de usar o componente
 * GradientIconButton em seu aplicativo.
 */
export function GradientIconButtonExample() {
  return (
    <Box padding="s24" gap="s24">
      <Text variant="text18Medium" textAlign="center">
        Exemplos do GradientIconButton
      </Text>

      {/* Botão circular apenas com ícone */}
      <Box gap="s12">
        <Text variant="text14Medium">Botão circular com ícone:</Text>
        <Box flexDirection="row" gap="s16" justifyContent="center">
          <GradientIconButton
            iconName="pix"
            onPress={() => console.log('PIX pressed')}
            size={56}
            iconSize={24}
            iconOnly
          />
          
          <GradientIconButton
            iconName="wallet"
            onPress={() => console.log('Wallet pressed')}
            size={56}
            iconSize={24}
            iconOnly
          />
          
          <GradientIconButton
            iconName="qrCode"
            onPress={() => console.log('QR Code pressed')}
            size={56}
            iconSize={24}
            iconOnly
          />
        </Box>
      </Box>

      {/* Botão circular com ícone e texto abaixo */}
      <Box gap="s12">
        <Text variant="text14Medium">Botão com ícone e texto abaixo:</Text>
        <Box flexDirection="row" gap="s16" justifyContent="center">
          <GradientIconButton
            iconName="transfer"
            title="Transferir"
            onPress={() => console.log('Transfer pressed')}
            size={56}
            iconSize={24}
            iconOnly
          />
          
          <GradientIconButton
            iconName="copyPaste"
            title="Copiar e Colar"
            onPress={() => console.log('Copy paste pressed')}
            size={56}
            iconSize={24}
            iconOnly
          />
        </Box>
      </Box>

      {/* Botão retangular com ícone e texto lado a lado */}
      <Box gap="s12">
        <Text variant="text14Medium">Botão retangular com ícone e texto:</Text>
        <GradientIconButton
          iconName="support"
          title="Suporte"
          onPress={() => console.log('Support pressed')}
          size={120}
          iconSize={20}
          iconOnly={false}
        />
      </Box>

      {/* Botão com cores customizadas */}
      <Box gap="s12">
        <Text variant="text14Medium">Botão com gradiente customizado:</Text>
        <GradientIconButton
          iconName="home"
          title="Home"
          onPress={() => console.log('Home pressed')}
          size={56}
          iconSize={24}
          gradientColors={['#FF6B6B', '#FF8E53']}
          iconOnly
        />
      </Box>

      {/* Botão em estado de carregamento */}
      <Box gap="s12">
        <Text variant="text14Medium">Botão em carregamento:</Text>
        <GradientIconButton
          iconName="wallet"
          title="Carregando..."
          onPress={() => console.log('Loading pressed')}
          size={56}
          iconSize={24}
          isLoading={true}
          iconOnly
        />
      </Box>

      {/* Botões com tamanhos diferentes */}
      <Box gap="s12">
        <Text variant="text14Medium">Diferentes tamanhos:</Text>
        <Box flexDirection="row" gap="s16" justifyContent="center" alignItems="center">
          <GradientIconButton
            iconName="bell"
            onPress={() => console.log('Small bell pressed')}
            size={40}
            iconSize={18}
            iconOnly
          />
          
          <GradientIconButton
            iconName="bell"
            onPress={() => console.log('Medium bell pressed')}
            size={56}
            iconSize={24}
            iconOnly
          />
          
          <GradientIconButton
            iconName="bell"
            onPress={() => console.log('Large bell pressed')}
            size={72}
            iconSize={30}
            iconOnly
          />
        </Box>
      </Box>
    </Box>
  );
}
