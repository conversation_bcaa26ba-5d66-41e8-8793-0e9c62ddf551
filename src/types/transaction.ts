export type Transaction = {
  id: string;
  amount: number;
  customer: {
    id: string;
    externalRef: string | null;
    name: string;
    email: string;
    phone: string;
    documentType: 'CPF' | 'CNPJ' | string; // ajuste conforme tipos válidos
    document: string;
    createdAt: string;
  };
  items: {
    id: string;
    title: string;
    amount: number;
    quantity: number;
    tangible: boolean;
    externalRef: string;
    transactionId: string;
    createdAt: string;
    updatedAt: string;
  }[];
  method: 'CREDIT_CARD' | 'PIX' | 'BOLETO' | string;
  status: 'FAILED' | 'PENDING' | 'PAID' | string;
  boleto: null | any;
  card: {
    id: string;
    number: string;
    holderName: string;
    holderDocument: string;
    expirationMonth: number;
    expirationYear: number;
    cvv: string;
    brand: string | null;
    createdAt: string;
    updatedAt: string;
    transactionId: string;
  } | null;
  pix: null | any; // ajuste se pix for utilizado
  description: string | null;
  installments: number | null;
  metadata: any | null;
  paidAt: string | null;
  postbackUrl: string | null;
  createdAt: string;
};

export type WithdrawalResponse = {
  data: Transaction;
  count: number;
  take: number;
  skip: number;
}