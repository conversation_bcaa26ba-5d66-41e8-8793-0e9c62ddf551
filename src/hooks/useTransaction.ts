import { useQuery } from "@tanstack/react-query";
import { Transaction, WithdrawalResponse } from "../types/transaction";
import api from "../services/apiConfig";


export function useTransaction() {
  const getTransactions = async (): Promise<Transaction> => {
    const response = await api.get<WithdrawalResponse>('/transactions?skip=50&take=10');  
    return response.data.data;
  }
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['transactions'],
    queryFn: () => getTransactions(),
  });

  return { transactions: data, isLoading, error };
}