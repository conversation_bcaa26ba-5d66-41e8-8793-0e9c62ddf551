import React from 'react';

import { IconBase } from '@/src/ui/components/Icon';
import { ClipPath, Defs, G, Path, Rect, Svg } from 'react-native-svg';

export function WalletCheckIcon({ size = 20, color = 'black' }: IconBase) {
  return (
    <Svg width={size} height={size} viewBox="0 0 12 11" fill="none">
      <G clip-path="url(#clip0_603_530)">
        <Path d="M1.97266 8.69608L2.42465 9.14807L3.39712 8.24865M8.50604 3.24018C8.39646 3.22192 8.28232 3.21735 8.16362 3.21735H3.59801C3.47017 3.21735 3.3469 3.22649 3.2282 3.24475C3.29212 3.11691 3.38343 2.99821 3.493 2.88863L4.97682 1.40024C5.27816 1.10192 5.68506 0.93457 6.10909 0.93457C6.53313 0.93457 6.94003 1.10192 7.24136 1.40024L8.04034 2.20836C8.33254 2.49599 8.48777 2.86124 8.50604 3.24018Z" stroke={color} strokeLinecap="round" strokeLinejoin="round" />
        <Path d="M10.4457 5.50058V7.78338C10.4457 9.15306 9.53254 10.0662 8.16286 10.0662H3.88489C4.02642 9.94747 4.14969 9.80137 4.24557 9.63701C4.4145 9.36308 4.51038 9.03892 4.51038 8.6965C4.51038 7.6875 3.69313 6.87026 2.68413 6.87026C2.13626 6.87026 1.64774 7.11223 1.31445 7.49118V5.50058C1.31445 4.25873 2.06321 3.39127 3.22744 3.24517C3.34615 3.2269 3.46942 3.21777 3.59726 3.21777H8.16286C8.28157 3.21777 8.39571 3.22234 8.50528 3.2406C9.68321 3.37757 10.4457 4.2496 10.4457 5.50058Z" stroke={color} strokeLinecap="round" strokeLinejoin="round" />
        <Path d="M10.4459 5.72852H9.07621C8.57399 5.72852 8.16309 6.13942 8.16309 6.64164C8.16309 7.14385 8.57399 7.55476 9.07621 7.55476H10.4459" stroke={color} strokeLinecap="round" strokeLinejoin="round" />
        <Path d="M4.51088 8.69636C4.51088 9.03878 4.41501 9.36294 4.24608 9.63687C4.08513 9.90738 3.8565 10.1313 3.58272 10.2866C3.30893 10.4419 2.99941 10.5233 2.68464 10.5226C2.36987 10.5233 2.06036 10.4419 1.78657 10.2866C1.51278 10.1313 1.28416 9.90738 1.12321 9.63687C0.949413 9.354 0.857728 9.02836 0.858402 8.69636C0.858402 7.68736 1.67565 6.87012 2.68464 6.87012C3.69364 6.87012 4.51088 7.68736 4.51088 8.69636Z" stroke={color} strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      </G>
      <Defs>
        <ClipPath id="clip0_603_530">
          <Rect width="10.9574" height="10.9574" fill={color} transform="translate(0.401367 0.0214844)" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}