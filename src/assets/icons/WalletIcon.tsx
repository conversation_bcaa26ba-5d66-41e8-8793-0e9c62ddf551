import React from 'react';

import { IconBase } from '@/src/ui/components/Icon';
import { Path, Svg } from 'react-native-svg';

export function WalletIcon({ size = 20, color = 'black' }: IconBase) {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32" fill="none">
      <Path d="M8.27759 0.497931C7.87597 0.569092 7.33378 0.882198 7.07941 1.18819C6.79828 1.53687 6.67779 1.8144 5.55324 4.81737L4.82813 6.74704C4.64824 7.22577 4.1904 7.54282 3.67899 7.54282C3.21043 7.54282 2.64816 7.5784 2.42726 7.6211C1.39643 7.82034 0.599873 8.54618 0.184861 9.64205C0.0786226 9.92441 0.0238787 10.2235 0.0232289 10.5252L0.00413034 19.391C-0.00925714 25.2262 0.0108241 28.8839 0.0509865 29.1614C0.251799 30.5419 1.26255 31.6093 2.56783 31.8085C3.20374 31.9011 26.826 31.9011 27.4685 31.8085C28.5262 31.6449 29.4231 30.8763 29.8314 29.7876C29.9252 29.5456 29.9452 29.0973 29.9787 27.2543L30.009 25.2317C30.0109 25.1079 30.0985 25.0021 30.2197 24.9771C30.5477 24.8989 30.9694 24.6142 31.1434 24.3438C31.4446 23.8742 31.458 23.682 31.4379 19.5334L31.4198 15.997C31.4185 15.7483 31.363 15.503 31.2572 15.278C31.0698 14.8724 30.7485 14.5948 30.3268 14.481C30.1405 14.4304 30.0102 14.2626 30.0073 14.0696L29.9787 12.1611C29.9452 10.1046 29.9318 9.89823 29.8047 9.56378C29.5034 8.78101 28.9947 8.20461 28.2986 7.86304C27.7631 7.60686 27.3481 7.54282 26.1633 7.54282C25.4659 7.54282 24.7763 7.39687 24.1387 7.11437L17.2204 4.04884C12.2604 1.8571 9.19464 0.53351 8.96036 0.490814C8.94339 0.487808 8.92667 0.484905 8.91027 0.482107C8.742 0.453405 8.44605 0.470379 8.27759 0.497931ZM15.1186 4.76756L19.8223 6.84797C20.1804 7.00634 20.0685 7.54118 19.677 7.54282C18.5561 7.54282 17.4432 7.35345 16.3853 6.9827L13.7196 6.04845C11.3768 5.22299 9.36867 4.55408 9.24819 4.55408C8.85326 4.55408 8.71269 4.78179 8.19058 6.19789L7.99633 6.73306C7.82681 7.20008 7.38866 7.51571 6.89199 7.52859C6.2427 7.54282 6.07535 7.52859 6.10213 7.45743C6.81836 5.52898 7.92952 2.61852 8.02323 2.41927C8.15711 2.13462 8.35792 2.00653 8.65914 1.99942C8.77962 1.9923 11.3299 3.08817 15.1186 4.76756ZM11.5709 6.89526C11.9186 7.01939 11.8304 7.53383 11.4613 7.53511L11.2898 7.5357L9.7915 7.54091C9.52289 7.54184 9.33396 7.27704 9.42222 7.02335C9.52263 6.74582 9.62973 6.45407 9.64981 6.3829C9.67659 6.31886 9.71675 6.26193 9.74352 6.26193C9.77699 6.26193 10.5936 6.54657 11.5709 6.89526ZM27.7765 9.29337C28.1245 9.49973 28.399 9.86265 28.5061 10.254C28.5462 10.3892 28.573 11.3641 28.573 12.4315C28.573 13.5024 27.7083 14.3722 26.6375 14.3785L24.945 14.3885C21.4174 14.4169 21.2969 14.424 20.8217 14.5735C19.0344 15.1499 17.7091 16.6158 17.2807 18.4873C17.1334 19.142 17.1401 20.3161 17.2941 20.9566C17.7425 22.8352 19.0144 24.2371 20.7748 24.8064C21.174 24.9384 21.5913 25.0073 22.0118 25.0106L24.9785 25.0341L26.6446 25.0473C27.7121 25.0557 28.573 25.9235 28.573 26.991C28.573 28.1367 28.5462 29.0404 28.4994 29.197C28.4124 29.5456 28.0107 30.0153 27.6694 30.179C27.415 30.3071 26.8527 30.3142 15.0115 30.3142H3.04335C2.77054 30.3142 2.50177 30.2483 2.25992 30.122C2.02564 29.9868 1.83152 29.8161 1.69765 29.6168C1.56651 29.4123 1.49656 29.1746 1.49604 28.9317L1.47675 19.8678C1.46337 13.6128 1.48345 10.3394 1.52361 10.1758C1.61732 9.82707 1.94532 9.43569 2.28 9.25779C2.46911 9.15961 2.67907 9.10835 2.89214 9.10835H15.0115H27.0719C27.3188 9.10835 27.5614 9.17208 27.7765 9.29337ZM29.9787 19.7113C29.9787 21.7942 28.2901 23.4828 26.2072 23.4828H25.8219C21.1965 23.4828 21.23 23.4828 20.4067 23.006C19.2888 22.3442 18.5993 21.0918 18.5993 19.7113C18.5993 18.0105 19.5766 16.6087 21.143 16.075C21.4776 15.9611 21.8726 15.9469 25.7483 15.9469L26.2072 15.9461C28.2891 15.9426 29.9787 17.6293 29.9787 19.7113Z" fill="#3D4045" />
      <Path d="M4.69644 12.2117C4.43538 12.3469 4.24796 12.7383 4.3082 13.0158C4.32828 13.1297 4.44208 13.3076 4.54918 13.4214C4.67777 13.5536 4.85352 13.6293 5.0379 13.6321L6.16906 13.6491C6.95223 13.6634 7.72201 13.6562 7.88266 13.6278C8.52526 13.5282 8.77962 12.8593 8.36461 12.3682C8.24147 12.2193 8.05909 12.132 7.86584 12.1294L6.53722 12.1121C5.15831 12.0978 4.87717 12.1121 4.69644 12.2117Z" fill={color} />
      <Path d="M4.67691 15.2495C4.5832 15.2993 4.45601 15.4559 4.38908 15.6053C4.28198 15.8259 4.27528 15.9042 4.34222 16.1177C4.38238 16.2529 4.49618 16.4308 4.58989 16.509C4.75723 16.6443 4.85764 16.6514 7.86982 16.6514H10.6405C10.8587 16.6514 11.0672 16.5612 11.2167 16.4023C11.4309 16.1746 11.451 16.1248 11.4175 15.8473C11.3907 15.6338 11.3171 15.4915 11.1698 15.3491C11.041 15.2258 10.8695 15.157 10.6911 15.157H7.90329C5.60064 15.157 4.80409 15.1783 4.67691 15.2495Z" fill={color} />
      <Path d="M21.5052 17.535C20.2735 17.962 19.6577 19.4919 20.24 20.7017C20.6216 21.5058 21.3043 21.9541 22.1478 21.9541C22.9979 21.9541 23.6672 21.5129 24.0689 20.6874C24.2228 20.3743 24.2496 20.2249 24.2496 19.7125C24.2563 19.2144 24.2228 19.0436 24.0889 18.7519C23.8814 18.2965 23.4731 17.8695 23.0581 17.6489C22.6565 17.4496 21.9135 17.3927 21.5052 17.535ZM22.4423 19.0294C22.71 19.1504 22.9108 19.556 22.8506 19.8478C22.7368 20.4099 22.0607 20.6376 21.6792 20.2462C21.5721 20.1395 21.465 19.9616 21.4449 19.8478C21.3847 19.5631 21.5855 19.1504 21.8398 19.0294C22.1076 18.9084 22.1678 18.9084 22.4423 19.0294Z" fill="#989898" />
    </Svg>
  );
}