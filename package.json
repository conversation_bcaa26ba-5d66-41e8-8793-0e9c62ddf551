{"name": "app-wite-tec", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "build-apk": "npx eas build --profile preview --platform android --local", "build-release": "npx eas build --profile production --platform ios --local"}, "dependencies": {"@expo/config-plugins": "10.1.2", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5", "@gorhom/portal": "^1.0.14", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.3.9", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@shopify/react-native-skia": "^2.1.1", "@shopify/restyle": "^2.4.5", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "date-fns": "^4.1.0", "expo": "~53.0.0", "expo-blur": "~14.1.4", "expo-constants": "~17.1.7", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.3", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.17", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "victory-native": "^41.17.4", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "react-native-svg-transformer": "^1.5.1", "reactotron-react-native": "^5.1.14", "typescript": "~5.8.3"}, "private": true}