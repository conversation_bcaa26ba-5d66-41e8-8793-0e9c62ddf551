import { Box, NavigationOption, Screen, Text } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";

const options: NavigationOption[] = [
  { type: "transfer" },
  { type: "QRCode" },
  { type: "copyandpaste" },
];

export default function TransferScreen() {

  return (
    <Screen
      scrollable
      customHeader={
        <HeaderPrimary
          title=" "
          canGoBack
        />}
    >

      <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27} mt="s14">
       {`Qual é o valor da${'\n'}transferência?`}
      </Text>

      <Box gap="s8" mt="s12">
        <Text fontSize={17} lineHeight={25} fontFamily="DMSansRegular" color="zinc50">
          Saldo disponível de
        </Text>
        <Text fontFamily="DMSansBold" color="gray900">R$100.000,00</Text>
      </Box>

      <Box mt="s12">
        <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27}>
          R$ 0,00
        </Text>
        <Text fontSize={17} lineHeight={25} fontFamily="DMSansRegular" color="zinc50">
          {`Transferir para`}
        </Text>
        <Text fontFamily="DMSansBold" color="gray900">R$100.000,00</Text>
      </Box>
    </Screen>
  );
} 