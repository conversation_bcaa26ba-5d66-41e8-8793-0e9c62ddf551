import { Box, GradientIconButton, NavigationOption, Screen, Text } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";

const options: NavigationOption[] = [
  { type: "transfer" },
  { type: "QRCode" },
  { type: "copyandpaste" },
];

export default function TransferScreen() {

  return (
    <Screen
      customHeader={
        <HeaderPrimary
          title=" "
          canGoBack
        />}
    >

    <Box backgroundColor="gray">
    <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27} mt="s14">
        {`Qual é o valor da${'\n'}transferência?`}
      </Text>

      <Box gap="s8" mt="s12">
        <Text fontSize={17} lineHeight={25} fontFamily="DMSansRegular" color="zinc50">
          Saldo disponível de
        </Text>
        <Text fontFamily="DMSansBold" color="gray900">R$100.000,00</Text>
      </Box>

      <Box
        borderBottomWidth={1}
        borderBottomColor="border"
        paddingBottom="s12"
        mt="s32"
      >
        <Text fontSize={25} fontWeight={'600'} fontFamily="DMSansSemiBold" lineHeight={27}>
          R$ 0,00
        </Text>
      </Box>

      <Box mt="s32" gap="s16">
        <Text variant="text14Medium">Escolha o método de transferência:</Text>

        <Box flexDirection="row" justifyContent="space-around" gap="s16">
          <GradientIconButton
            iconName="pix"
            onPress={() => console.log('PIX pressed')}
            iconOnly
          />

          <GradientIconButton
            iconName="qrCode"
            title="QR Code"
            onPress={() => console.log('QR Code pressed')}
            iconOnly
          />


        </Box>
      </Box>
      <GradientIconButton
        iconName="copyPaste"
        title="Copiar e Colar"
        onPress={() => console.log('Copy paste pressed')}
        iconOnly
        style={{
          position: 'absolute',
          bottom: 0,
          right: 0,
        }}
      />
    </Box>
    </Screen>
  );
} 