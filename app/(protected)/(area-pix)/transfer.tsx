import { Box, Button, GradientIconButton, Screen, TextInput } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { Text } from "@/src/ui/components/Text";
import { formatCurrency } from "@/src/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { Keyboard, Platform } from "react-native";
import { z } from "zod";

// Schema de validação por step
const amountSchema = z.object({
  amount: z.string()
    .min(1, "Valor é obrigatório")
    .refine(
      (val) => {
        const numValue = parseFloat(val.replace(',', '.'));
        return !isNaN(numValue) && numValue > 0;
      },
      { message: "Digite um valor válido maior que zero" }
    )
    .refine(
      (val) => {
        const numValue = parseFloat(val.replace(',', '.'));
        return numValue <= 100000; // limite de R$ 100.000
      },
      { message: "Valor excede o limite permitido" }
    ),
});

const recipientSchema = z.object({
  pixKey: z.string()
    .min(1, "Chave PIX é obrigatória")
    .refine(
      (val) => {
        // Validações básicas para diferentes tipos de chave PIX
        const email = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val);
        const phone = /^\(\d{2}\)\s\d{4,5}-\d{4}$/.test(val) || /^\d{10,11}$/.test(val);
        const cpf = /^\d{3}\.\d{3}\.\d{3}-\d{2}$/.test(val) || /^\d{11}$/.test(val);
        const cnpj = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/.test(val) || /^\d{14}$/.test(val);
        const randomKey = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(val);

        return email || phone || cpf || cnpj || randomKey;
      },
      { message: "Digite uma chave PIX válida" }
    ),
});


type AmountFormData = z.infer<typeof amountSchema>;
type RecipientFormData = z.infer<typeof recipientSchema>;

export default function TransferScreen() {
  const router = useRouter();
  const { step, transferValue, isEdit } = useLocalSearchParams<{
    step?: 'amount' | 'recipient';
    transferValue?: string;
    isEdit?: string;
  }>();


  const IS_EDIT_MODE = isEdit === 'true';
  const currentStep = step || 'amount';
  const isAmountStep = currentStep === 'amount';
  const isRecipientStep = currentStep === 'recipient';

  // Form para step de valor
  const amountForm = useForm<AmountFormData>({
    resolver: zodResolver(amountSchema),
    defaultValues: {
      amount: IS_EDIT_MODE ? transferValue : '0,00',
    },
    mode: 'onChange',
  });

  // Form para step de destinatário
  const recipientForm = useForm<RecipientFormData>({
    resolver: zodResolver(recipientSchema),
    defaultValues: {
      pixKey: '',
    },
    mode: 'onChange',
  });

  const handleAmountSubmit = (data: AmountFormData) => {
    router.push({
      pathname: '/transfer',
      params: {
        step: 'recipient',
        transferValue: `${formatCurrency(Number(data.amount))}`
      }
    });
  };

  const handleRecipientSubmit = (data: RecipientFormData) => {
    console.log('Processing transfer:', {
      amount: transferValue,
      pixKey: data.pixKey
    });

    // chamada da API
    // await processTransfer({ amount: transferValue, pixKey: data.pixKey });

    router.push('/');
  };

  const handleSubmit = isAmountStep
    ? amountForm.handleSubmit(handleAmountSubmit)
    : recipientForm.handleSubmit(handleRecipientSubmit);

  const isFormValid = isAmountStep
    ? amountForm.formState.isValid
    : recipientForm.formState.isValid;

  return (
    <Screen
      customHeader={
        <HeaderPrimary
          title=" "
          canGoBack
        />}
    >
      <Box height="100%" paddingTop="s16">
        {isAmountStep ? (
          <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27} mt="s14">
            {`Qual é o valor da${'\n'}transferência?`}
          </Text>
        ) : (
          <Box mt="s14">
            <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27}>
              Para qual chave você quer
            </Text>
            <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27}>
              transferir <Text fontWeight={'700'} fontSize={22} fontFamily="DMSansBold">{transferValue}</Text>?
            </Text>
          </Box>
        )}

        {isAmountStep && (
          <Box gap="s8" mt="s12">
            <Text fontSize={17} fontFamily="DMSansRegular" color="zinc50">
              Saldo disponível de
            </Text>
            <Text fontFamily="DMSansBold" color="gray900">R$100.000,00</Text>
          </Box>
        )}

        {/* primeira etapa */}
        {isAmountStep && (
          <Box mt="s32">
            <Box
              borderBottomWidth={1}
              borderBottomColor="border"

            >
              <Box flexDirection="row">
                <Box flex={1}>
                  <Controller
                    control={amountForm.control}
                    name="amount"
                    render={({ field: { onChange, value } }) => (
                      <TextInput
                        label=""
                        placeholder="0,00"
                        value={formatCurrency(Number(value.replace(/[^0-9]/g, '')))}
                        onChangeText={(text) => onChange(text.replace(/[^0-9]/g, ''))}
                        keyboardType="numeric"
                        style={{
                          fontSize: 25,
                          fontWeight: '600',
                          fontFamily: 'DMSansSemiBold',
                          color: '#1F1F1F',
                          paddingHorizontal: 0,
                          flex: 1,
                        }}
                        boxProps={{
                          backgroundColor: 'background',
                        }}
                      />
                    )}
                  />
                </Box>
              </Box>
            </Box>

            {/* Erro de validação */}
            {amountForm.formState.errors.amount && (
              <Text fontSize={14} mt="s8" style={{ color: '#FF0000' }}>
                {amountForm.formState.errors.amount.message}
              </Text>
            )}
          </Box>
        )}

        {/* Input de chave PIX - segunda etapa */}
        {isRecipientStep && (
          <Box mt="s32">
            <Box
              borderBottomWidth={1}
              borderBottomColor="border"
              flexDirection="row"
            >
              <Box flex={1}>
                <Controller
                  control={recipientForm.control}
                  name="pixKey"
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      label=""
                      placeholderTextColor="#3D40457F"
                      placeholder="CPF/CNPJ, E-mail ou Celular"
                      value={value}
                      onChangeText={onChange}
                      style={{
                        fontSize: 16,
                        borderWidth: 0,
                        paddingHorizontal: 0,
                        backgroundColor: 'transparent',
                      }}
                      boxProps={{
                        backgroundColor: 'background',
                      }}
                    />
                  )}
                />
              </Box>
            </Box>

            {/* Erro de validação */}
            {recipientForm.formState.errors.pixKey && (
              <Text fontSize={14} mt="s8" style={{ color: '#FF0000' }}>
                {recipientForm.formState.errors.pixKey.message}
              </Text>
            )}
          </Box>
        )}

        {isRecipientStep && (
          <Box alignItems="center" mt="s56">
            <Button
              variant="gradient"
              title="Transferir"
              onPress={() => router.push({
                pathname: '/confirmation',
                params: {
                  transferValue: transferValue,
                },
              })}
              boxProps={{
                width: '70%',
                borderRadius: 'rounded',
              }}
            />
          </Box>
        )}



        <Box style={{
          flex: 1,
        }}>



        </Box>
        {!isRecipientStep && (
          <GradientIconButton
            iconName="arrowRight"
            onPress={() => {
              Keyboard.dismiss();
              handleSubmit();
            }}
            iconOnly
            disabled={!isFormValid}
            style={{
              position: 'absolute',
              bottom: Platform.OS === 'ios' ? 16 : 65,
              right: 20,
              opacity: isFormValid ? 1 : 0.5,
            }}
          />
        )}

      </Box>
    </Screen>
  );
} 