import { dataWithdrawal } from "@/mock";
import { Box, CardTransaction, NavigationOption, OptionNavigate, Screen, Text } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { FlatList } from "react-native";

const options: NavigationOption[] = [
  { type: "transfer" },
  { type: "QRCode" },
  { type: "copyandpaste" },
];

export default function AreaPixScreen() {

  return (
    <Screen
      scrollable
      customHeader={
        <HeaderPrimary
          title="Área Pix"
          canGoBack
        />}
    >
      <Box flex={1} alignItems="center" height="100%" justifyContent="center">
        <Text marginTop="s32" textAlign="center" variant="text14Regular">
          Envie e receba pagamento a qualquer hora e dia da semana
        </Text>

        <Box gap="s16" marginTop="s24" flexDirection="row" paddingHorizontal="s20">
          {options.map((option, index) => (
            <OptionNavigate key={index} type={option.type} fontSize="medium" />
          ))}
        </Box>

        <Box flex={1} height="100%" mt="s56" borderRadius="secondary" overflow="hidden" >
          <FlatList
            data={dataWithdrawal}
            renderItem={({ item }) => <CardTransaction data={item} />}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{ gap: 16 }}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={() => (
              <Box alignItems="center" gap="s20" justifyContent="center" flex={1}>
                <Text variant="text14Regular" color="gray500">Nenhuma transação encontrada</Text>
              </Box>
            )}
            style={{
              backgroundColor: 'white',
              padding: 20,
              // flexGrow: 1,
            }}
            ListHeaderComponent={() => (
              <Box
                marginTop="s16"
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Text variant="text18Medium">Histórico de saques</Text>
              </Box>
            )}
          />
        </Box>
      </Box>
    </Screen>
  );
} 