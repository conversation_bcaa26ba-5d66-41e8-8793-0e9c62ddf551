import { Box, Button, Icon, Screen, Text, TouchableOpacityBox } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { useLocalSearchParams, useRouter } from "expo-router";

export default function ConfirmationScreen() {
  const router = useRouter();
  const { transferValue } = useLocalSearchParams();
  return (
    <Screen
      customHeader={
        <HeaderPrimary
          title=" "
          canGoBack
        />}
    >
      <Box height="100%" paddingTop="s16">
        <Text
          fontSize={25}
          fontFamily="DMSansMedium"
          color="zinc"
          lineHeight={25}
        >
          Transferindo
        </Text>
        <Box flexDirection="row" justifyContent="space-between" alignItems="center" mt="s24">
          <Text
            fontSize={24}
            lineHeight={26}
            fontFamily="DMSansBold"
            color="zinc50"
          >
            {transferValue}
          </Text>
          <TouchableOpacityBox
            flexDirection="row"
            alignItems="center"
            gap="s4"
            onPress={() => router.push({
              pathname: '/transfer',
              params: {
                transferValue: transferValue,
                isEdit: 'true',
              },
            })}>
            <Icon name="edit" size={19} color="zinc50" />
            <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">Alterar</Text>
          </TouchableOpacityBox>
        </Box>

        <Box alignItems="center" mt="s56">
          <Button
            variant="gradient"
            title="Transferir"
            onPress={() => router.push({
              pathname: '/confirmation',
              params: {
                transferValue: transferValue,
              },
            })}
            boxProps={{
              width: '70%',
              borderRadius: 'rounded',
            }}
          />
        </Box>
      </Box>
    </Screen>
  );
} 