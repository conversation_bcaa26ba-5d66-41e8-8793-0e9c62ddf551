import { Box, Button, GradientIconButton, Screen, TextInput } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { Text } from "@/src/ui/components/Text";
import { formatCurrency } from "@/src/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { Keyboard, Platform } from "react-native";
import { z } from "zod";

// Schema de validação por step
const amountSchema = z.object({
  amount: z.string()
    .min(1, "Valor é obrigatório")
    .refine(
      (val) => {
        const numValue = parseFloat(val.replace(',', '.'));
        return !isNaN(numValue) && numValue > 0;
      },
      { message: "Digite um valor válido maior que zero" }
    )
    .refine(
      (val) => {
        const numValue = parseFloat(val.replace(',', '.'));
        return numValue <= 100000; // limite de R$ 100.000
      },
      { message: "Valor excede o limite permitido" }
    ),
});

const recipientSchema = z.object({
  pixKey: z.string()
    .min(1, "Chave PIX é obrigatória")
    .refine(
      (val) => {
        // Validação básica para diferentes tipos de chave PIX
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const phoneRegex = /^\(\d{2}\)\s\d{4,5}-\d{4}$/;
        const cpfRegex = /^\d{3}\.\d{3}\.\d{3}-\d{2}$/;
        const cnpjRegex = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/;
        const randomKeyRegex = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i;
        
        return emailRegex.test(val) || phoneRegex.test(val) || cpfRegex.test(val) || cnpjRegex.test(val) || randomKeyRegex.test(val);
      },
      { message: "Chave PIX inválida" }
    ),
});

type AmountFormData = z.infer<typeof amountSchema>;
type RecipientFormData = z.infer<typeof recipientSchema>;

export default function SendPixScreen() {
  const router = useRouter();
  const { step, transferValue, isEdit } = useLocalSearchParams<{
    step?: 'amount' | 'recipient';
    transferValue?: string;
    isEdit?: string;
  }>();

  const IS_EDIT_MODE = isEdit === 'true';
  const currentStep = step || 'amount';
  const isAmountStep = currentStep === 'amount';
  const isRecipientStep = currentStep === 'recipient';

  // Form para step de valor
  const amountForm = useForm<AmountFormData>({
    resolver: zodResolver(amountSchema),
    defaultValues: {
      amount: IS_EDIT_MODE ? transferValue : '0,00',
    },
    mode: 'onChange',
  });

  // Form para step de destinatário
  const recipientForm = useForm<RecipientFormData>({
    resolver: zodResolver(recipientSchema),
    defaultValues: {
      pixKey: '',
    },
    mode: 'onChange',
  });

  const handleAmountSubmit = (data: AmountFormData) => {
    router.push({
      pathname: '/send-pix',
      params: {
        step: 'recipient',
        transferValue: `${formatCurrency(Number(data.amount))}`
      }
    });
  };

  const handleRecipientSubmit = (data: RecipientFormData) => {
    console.log('Processing transfer:', {
      amount: transferValue,
      pixKey: data.pixKey
    });

    // Navegar para confirmação
    router.push({
      pathname: '/confirmation',
      params: {
        transferValue: transferValue,
        pixKey: data.pixKey
      }
    });
  };

  const handleBackPress = () => {
    if (isRecipientStep) {
      router.push({
        pathname: '/send-pix',
        params: {
          step: 'amount',
          transferValue: transferValue
        }
      });
    } else {
      router.back();
    }
  };

  return (
    <Screen
      customHeader={
        <HeaderPrimary
          title={isAmountStep ? "Qual é o valor da transferência?" : "Para quem você quer transferir?"}
          canGoBack
          onBackPress={handleBackPress}
        />
      }
      flex={1}
    >
      <Box flex={1} paddingTop="s16">
        {isAmountStep && (
          <>
            <Box gap="s8" mt="s12">
              <Text fontSize={17} lineHeight={25} fontFamily="DMSansRegular" color="zinc50">
                Saldo disponível de
              </Text>
              <Text fontFamily="DMSansBold" color="gray900">R$100.000,00</Text>
            </Box>

            <Controller
              control={amountForm.control}
              name="amount"
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Box mt="s32">
                  <TextInput
                    value={value}
                    onChangeText={(text) => {
                      // Formatar como moeda
                      const numericValue = text.replace(/[^\d]/g, '');
                      const formattedValue = (Number(numericValue) / 100).toLocaleString('pt-BR', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      });
                      onChange(formattedValue);
                    }}
                    placeholder="R$ 0,00"
                    keyboardType="numeric"
                    textProps={{
                      fontSize: 25,
                      fontWeight: '600',
                      fontFamily: "DMSansSemiBold",
                      lineHeight: 27,
                    }}
                    boxProps={{
                      borderBottomWidth: 1,
                      borderBottomColor: "border",
                      paddingBottom: "s12",
                      backgroundColor: "transparent",
                    }}
                    errorMessage={error?.message}
                  />
                </Box>
              )}
            />

            <Box mt="s32" gap="s16">
              <Text variant="text14Medium">Escolha o método de transferência:</Text>
              
              <Box flexDirection="row" justifyContent="space-around" gap="s16">
                <GradientIconButton
                  iconName="pix"
                  title="PIX"
                  onPress={() => console.log('PIX pressed')}
                  iconOnly
                />
                
                <GradientIconButton
                  iconName="qrCode"
                  title="QR Code"
                  onPress={() => console.log('QR Code pressed')}
                  iconOnly
                />
                
                <GradientIconButton
                  iconName="copyPaste"
                  title="Copiar e Colar"
                  onPress={() => console.log('Copy paste pressed')}
                  iconOnly
                />
              </Box>
            </Box>

            <Box flex={1} justifyContent="flex-end" paddingBottom="s24">
              <Button
                variant="gradient"
                title="Continuar"
                onPress={amountForm.handleSubmit(handleAmountSubmit)}
                disabled={!amountForm.formState.isValid}
              />
            </Box>
          </>
        )}

        {isRecipientStep && (
          <>
            <Box gap="s8" mt="s12">
              <Text fontSize={17} lineHeight={25} fontFamily="DMSansRegular" color="zinc50">
                Valor da transferência
              </Text>
              <Text fontFamily="DMSansBold" color="gray900">{transferValue}</Text>
            </Box>

            <Controller
              control={recipientForm.control}
              name="pixKey"
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Box mt="s32">
                  <TextInput
                    value={value}
                    onChangeText={onChange}
                    placeholder="Digite a chave PIX"
                    autoCapitalize="none"
                    keyboardType="default"
                    label="Chave PIX"
                    errorMessage={error?.message}
                  />
                </Box>
              )}
            />

            <Box flex={1} justifyContent="flex-end" paddingBottom="s24">
              <Button
                variant="gradient"
                title="Continuar"
                onPress={recipientForm.handleSubmit(handleRecipientSubmit)}
                disabled={!recipientForm.formState.isValid}
              />
            </Box>
          </>
        )}
      </Box>
    </Screen>
  );
}
