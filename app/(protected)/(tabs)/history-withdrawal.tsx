import { Box, CardTransaction, Screen, Text } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { useState } from "react";
import { Dimensions, FlatList } from "react-native";
import Svg, { Circle, Defs, LinearGradient, Path, Stop } from "react-native-svg";

enum WithdrawalType {
  PIX = 'pix',
  CRYPTO = 'crypto',
}

const screenWidth = Dimensions.get('window').width;

// Dados mockados para o gráfico
const transactionData = [
  { x: 1, y: 800 },
  { x: 2, y: 1200 },
  { x: 3, y: 900 },
  { x: 4, y: 1800 },
  { x: 5, y: 1400 },
  { x: 6, y: 2354 },
];

const months = [
  { id: 1, label: "Out", value: 1 },
  { id: 2, label: "Nov", value: 2 },
  { id: 3, label: "Dez", value: 3 },
  { id: 4, label: "<PERSON>", value: 4 },
  { id: 5, label: "Fev", value: 5 },
  { id: 6, label: "Mar", value: 6 },
];

export default function WithdrawalScreen() {
  const [selectedValue, setSelectedValue] = useState<WithdrawalType>(WithdrawalType.PIX);
  const [selectedMonth, setSelectedMonth] = useState(4); // Jan é o mês selecionado

  const selectedDataPoint = transactionData.find(point => point.x === selectedMonth);
  const transactionCount = selectedDataPoint?.y || 2354;

  // Calcular dimensões do gráfico
  const chartWidth = screenWidth; // Usar toda a largura da tela
  const chartHeight = 200;
  const padding = 40;
  const graphWidth = chartWidth - (padding * 2);
  const graphHeight = chartHeight - (padding * 2);

  // Encontrar valores mínimos e máximos
  const minY = Math.min(...transactionData.map(d => d.y));
  const maxY = Math.max(...transactionData.map(d => d.y));
  const yRange = maxY - minY;

  // Gerar pontos do gráfico
  const points = transactionData.map((point, index) => {
    const x = padding + (index / (transactionData.length - 1)) * graphWidth;
    const y = padding + graphHeight - ((point.y - minY) / yRange) * graphHeight;
    return { x, y, original: point };
  });

  // Gerar path para a linha com curvas suaves
  const generateSmoothPath = (points: {x: number, y: number, original: any}[]) => {
    if (points.length < 2) return '';
    
    let path = `M ${points[0].x} ${points[0].y}`;
    
    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const curr = points[i];
      const next = points[i + 1];
      
      if (next) {
        // Curva Bézier mais suave
        const tension = 0.3; // Controla a suavidade da curva
        const cp1x = prev.x + (curr.x - prev.x) * (1 - tension);
        const cp1y = prev.y + (curr.y - prev.y) * (1 - tension);
        const cp2x = curr.x - (next.x - curr.x) * tension;
        const cp2y = curr.y - (next.y - curr.y) * tension;
        
        path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${curr.x} ${curr.y}`;
      } else {
        // Último ponto - linha direta
        path += ` L ${curr.x} ${curr.y}`;
      }
    }
    
    return path;
  };

  const linePath = generateSmoothPath(points);
  
  // Gerar path para a área com curvas suaves
  const areaPath = generateSmoothPath(points) + 
    ` L ${points[points.length - 1].x} ${chartHeight} L ${points[0].x} ${chartHeight} Z`;

  // Gerar linhas de grade verticais
  const gridLines = months.map((_, index) => {
    const x = padding + (index / (months.length - 1)) * graphWidth;
    const point = points[index];
    return { x, y1: padding, y2: point.y };
  });

  return (
    <Screen
      scrollable
      customHeader={
        <HeaderPrimary
          title="Histórico de transação"
          canGoBack={true}
        />}
    >
        <Box gap="s24">
          {/* Seção do Gráfico */}
        <Box
          paddingVertical="s16"
          borderRadius="default"
          marginTop="s16"
          style={{ marginLeft: -24, marginRight: -24 }} // Remove o padding do Screen
        >
          {/* Título e valor */}
          <Box alignItems="center" gap="s12" marginBottom="s24" paddingHorizontal="s16">
            <Text variant="text16Medium" color="gray500">Transações</Text>
            <Text variant="text25" color="black">{transactionCount.toLocaleString()}</Text>
          </Box>

          {/* Gráfico */}
          <Box width="100%" height={chartHeight}>
            <Svg width="100%" height={chartHeight} viewBox={`0 0 ${chartWidth} ${chartHeight}`}>
              {/* Definição do gradiente */}
              <Defs>
                <LinearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <Stop offset="0%" stopColor="#cecece" stopOpacity={1} />
                  <Stop offset="100%" stopColor="#F9FAFB" stopOpacity={0.3} />
                </LinearGradient>
                <LinearGradient id="lineGradient" x1="0%" y1="100%" x2="0%" y2="0%">
                  <Stop offset="0%" stopColor="#D1D5DB" />
                  <Stop offset="100%" stopColor="#000000" />
                </LinearGradient>
              </Defs>
              
              {/* Linhas de grade verticais */}
              {gridLines.map((line, index) => (
                <Path
                  key={index}
                  d={`M ${line.x} ${line.y1} L ${line.x} ${line.y2}`}
                  stroke="#969798"
                  strokeWidth={0.5}
                  opacity={0.3}
                />
              ))}
              
              {/* Área preenchida com gradiente */}
              <Path
                d={areaPath}
                fill="url(#areaGradient)"
              />
              
              {/* Linha do gráfico */}
              <Path
                d={linePath}
                stroke="url(#lineGradient)"
                strokeWidth={6}
                strokeLinecap="round"
                fill="none"
              />
              
              {/* Ponto selecionado */}
              {selectedDataPoint && (
                <Circle
                  cx={points[selectedMonth - 1]?.x || 0}
                  cy={points[selectedMonth - 1]?.y || 0}
                  r={8}
                  fill="white"
                  stroke="#000000"
                  strokeWidth={4}
                />
              )}
            </Svg>
          </Box>

          {/* Seleção de meses */}
          <Box
            flexDirection="row"
            justifyContent="space-between"
            marginTop="s16"
            paddingHorizontal="s16"
          >
            {months.map((month) => (
              <Box
                key={month.id}
                paddingHorizontal="s12"
                paddingVertical="s8"
                borderRadius="default"
                backgroundColor={selectedMonth === month.value ? "black" : "transparent"}
                onTouchEnd={() => setSelectedMonth(month.value)}
              >
                <Text
                  variant="text14Medium"
                  color={selectedMonth === month.value ? "white" : "gray500"}
                >
                  {month.label}
                </Text>
              </Box>
            ))}
          </Box>
        </Box>

        {/* <FlatList
          data={[1, 2, 3, 4, 5]}
          renderItem={({ item }) => <CardTransaction />}
          keyExtractor={(item) => item.toString()}
          contentContainerStyle={{ gap: 16 }}
          scrollEnabled={false}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={() => (
            <Box
              marginTop="s36"
              flexDirection="row"
              justifyContent="space-between"
              alignItems="center"
            >
              <Text variant="text18Medium">Histórico de saques</Text>
            </Box>
          )}
        /> */}
      </Box>
    </Screen>
  );
}