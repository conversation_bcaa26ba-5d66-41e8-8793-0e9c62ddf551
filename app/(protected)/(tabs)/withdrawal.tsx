import { useWalletData } from "@/src/hooks";
import { PixKeyType } from "@/src/types/pixKeyType";
import { Box, CardBalance, Screen } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { formatCurrency } from "@/src/utils";
import { validatePixKey } from "@/src/utils/regex";
import { useRef, useState } from "react";
import { TextInput as RNTextInput } from "react-native";

enum WithdrawalType {
  PIX = 'PIX',
  CRYPTO = 'crypto',
}

export default function WithdrawalScreen() {
  const [selectedValue, setSelectedValue] = useState<WithdrawalType>(WithdrawalType.PIX);
  const { balance, withdrawal, createWithdrawal } = useWalletData();
  const [pixKey, setPixKey] = useState('');
  const [pixKeyType, setPixKeyType] = useState<PixKeyType>(PixKeyType.UNKNOWN);
  const [value, setValue] = useState<number>(0);
  const inputRef = useRef<RNTextInput>(null);
  const inputRefValue = useRef<RNTextInput>(null);

  function focusInput() {
    inputRef.current?.focus();
  }

  function focusInputValue() {
    inputRefValue.current?.focus();
  }

  const handlePixKeyChange = (text: string) => {
    setPixKey(text);
    setPixKeyType(validatePixKey(text));
  };

  function handleWithdrawal() {
    try {
      createWithdrawal({
        amount: value,
        pixKey: pixKey,
        pixKeyType: pixKeyType,
        method: selectedValue,
      });
    } catch (error) {
      console.log(error);
    }
  }
  return (
    <Screen
      scrollable
      customHeader={
        <HeaderPrimary
          title="Carteira"
          canGoBack={true}
        />}
    >
      <Box gap="s24">

        <CardBalance
          boxProps={{ marginTop: 's20' }}
          label="Saldo disponível"
          value={formatCurrency(balance?.balance ?? 0)}
        />
      </Box>
    </Screen>
  );
}