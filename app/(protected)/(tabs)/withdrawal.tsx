import { dataWithdrawal } from "@/mock";
import { useWalletData } from "@/src/hooks";
import { PixKeyType } from "@/src/types/pixKeyType";
import { Box, Button, CardBalance, CardTransaction, Screen, Text, TextInput as TextInputComponent } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { formatCurrency } from "@/src/utils";
import { validatePixKey } from "@/src/utils/regex";
import { useRef, useState } from "react";
import { FlatList, Keyboard, Pressable, TextInput as RNTextInput, TouchableWithoutFeedback } from "react-native";

enum WithdrawalType {
  PIX = 'PIX',
  CRYPTO = 'crypto',
}

export default function WithdrawalScreen() {
  const [selectedValue, setSelectedValue] = useState<WithdrawalType>(WithdrawalType.PIX);
  const { balance, withdrawal, createWithdrawal } = useWalletData();
  const [pixKey, setPixKey] = useState('');
  const [pixKeyType, setPixKeyType] = useState<PixKeyType>(PixKeyType.UNKNOWN);
  const [value, setValue] = useState<number>(0);
  const inputRef = useRef<RNTextInput>(null);
  const inputRefValue = useRef<RNTextInput>(null);

  function focusInput() {
    inputRef.current?.focus();
  }

  function focusInputValue() {
    inputRefValue.current?.focus();
  }

  const handlePixKeyChange = (text: string) => {
    setPixKey(text);
    setPixKeyType(validatePixKey(text));
  };

  function handleWithdrawal() {
    try {
      createWithdrawal({
        amount: value,
        pixKey: pixKey,
        pixKeyType: pixKeyType,
        method: selectedValue,
      });
    } catch (error) {
      console.log(error);
    }
  }
  return (
    <Screen
      scrollable
      customHeader={
        <HeaderPrimary
          title="Área de saques"
          canGoBack={true}
        />}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <Box gap="s24">
          <CardBalance
            boxProps={{ marginTop: 's20' }}
            label="Saldo disponível"
            value={formatCurrency(balance?.balance ?? 0)}
          />

          <Box
            backgroundColor="iconGray"
            flexDirection="row"
            borderRadius="default"
            padding="s8"
            gap="s8"
          >
            <Button
              variant="light"
              title="Enviar Pix"
              onPress={() => setSelectedValue(WithdrawalType.PIX)}
              textProps={{
                fontFamily: 'DMSansMedium',
                fontSize: 16,
                color: selectedValue === WithdrawalType.PIX ? 'midnightBlack' : 'gray500'
              }}
              boxProps={{
                width: '100%',
                backgroundColor: selectedValue === WithdrawalType.PIX ? 'white' : 'iconGray',
              }}
            />

            <Button
              variant="light"
              title="Saque Cripto"
              onPress={() => setSelectedValue(WithdrawalType.CRYPTO)}
              textProps={{
                fontFamily: 'DMSansMedium',
                fontSize: 16,
                color: selectedValue === WithdrawalType.CRYPTO ? 'midnightBlack' : 'gray500'
              }}
              boxProps={{
                width: '100%',
                backgroundColor: selectedValue === WithdrawalType.CRYPTO ? 'white' : 'iconGray'
              }}
            />
          </Box>

          <Pressable onPress={focusInput}>
            <Box
              paddingHorizontal="s24"
              paddingVertical="s32"
              backgroundColor="iconGray"
              borderRadius="default"
              gap="s16"
            >
              <Text
                variant="text17Medium"
                color="gray500"
              >
                Digite sua chave pix
              </Text>
              <Box gap="s8">
                <TextInputComponent
                  ref={inputRef}
                  label=""
                  value={pixKey}
                  onChangeText={handlePixKeyChange}
                  placeholderTextColor="zinc200"
                  placeholder="Celular, CPF, CNPJ, E-mail, Chave Aleatória ou Pix Copia e Cola"
                  textProps={{
                    variant: "text11",
                    color: "zinc200",
                  }}
                  boxProps={{
                    backgroundColor: "transparent",
                    borderWidth: 0
                  }}
                />
                {pixKey.length > 0 && (
                  <Text variant="text11" color={pixKeyType === PixKeyType.UNKNOWN ? "gray500" : "text"}>
                    Tipo: {pixKeyType}
                  </Text>
                )}
              </Box>
            </Box>
          </Pressable>

          <Pressable onPress={focusInputValue}>
            <Box
              borderWidth={1}
              paddingHorizontal="s24"
              paddingVertical="s32"
              borderColor="border"
              borderRadius="default"
              gap="s20"
            >
              <Text variant="text14Regular" color="gray500">
                Selecione o valor
              </Text>
              <Box flexDirection="row" alignItems="center">
                <TextInputComponent
                  ref={inputRefValue}
                  label=""
                  value={formatCurrency(value)}
                  onChangeText={(text) => setValue(Number(text.replace(/[^0-9]/g, '')))}
                  placeholderTextColor="zinc200"
                  placeholder="05,00"
                  style={{
                    fontSize: 24,
                    fontFamily: 'DMSansSemiBold',
                    color: "gray900",
                    lineHeight: 0,
                  }}
                  boxProps={{
                    backgroundColor: "transparent",
                    height: 30,
                    borderWidth: 0
                  }}
                />
              </Box>
            </Box>
          </Pressable>
          <Button
            variant="dark"
            icon="receiveMoney"
            textProps={{ fontFamily: 'DMSansSemiBold', textTransform: 'uppercase' }}
            title="Sacar"
            disabled={value === 0 || pixKey.length === 0 || pixKeyType === PixKeyType.UNKNOWN}
            onPress={handleWithdrawal}
          />

          <Box flex={1} minHeight={200} borderRadius="secondary" overflow="hidden" >
            <FlatList
              data={dataWithdrawal}
              renderItem={({ item }) => <CardTransaction data={item} />}
              keyExtractor={(item) => item.id}
              contentContainerStyle={{ gap: 16 }}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={() => (
                <Box alignItems="center" gap="s20" justifyContent="center" flex={1}>
                  <Text variant="text14Regular" color="gray500">Nenhuma transação encontrada</Text>
                </Box>
              )}
              style={{
                backgroundColor: 'white',
                padding: 20
              }}
              ListHeaderComponent={() => (
                <Box
                  marginTop="s16"
                  flexDirection="row"
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Text variant="text18Medium">Histórico de saques</Text>
                </Box>
              )}
            />
          </Box>
        </Box>
      </TouchableWithoutFeedback>
    </Screen>
  );
}