import { dataWithdrawal } from "@/mock";
import { useResponsiveWidth, useWalletData } from "@/src/hooks";
import { useTransaction } from "@/src/hooks/useTransaction";
import { Box, CardBalance, CardTransaction, HeaderHome, NavigationOption, OptionNavigate, Screen } from "@/src/ui/components";
import { Text } from "@/src/ui/components/Text";
import { useAppTheme } from "@/src/ui/theme/useAppTheme";
import { formatCurrency } from "@/src/utils/format-current";
import { FlatList } from "react-native";

export default function HomeScreen() {
  const { report, withdrawal, balance } = useWalletData();
  const { borderRadii, colors} = useAppTheme()
  const { transactions } = useTransaction();

  const options: NavigationOption[] = [
    { type: "pix" },
    { type: "wallet" },
    { type: "transactions" },
    { type: "support" },
  ];

  // Calcula a largura responsiva
  const optionWidth = useResponsiveWidth({
    itemCount: options.length,
    horizontalPadding: 40,
    gapSize: 16
  });

  return (
    <Screen
      scrollable
      customHeader={<HeaderHome />}
      style={{ paddingHorizontal: 0 }}
    >
      <CardBalance
        boxProps={{ marginTop: 's20', marginHorizontal: 's20' }}
        label="Disponível"
        value={formatCurrency(balance?.balance ?? 0)}
      />

      <Box gap="s16" marginTop="s24" flexDirection="row" paddingHorizontal="s20">
        {options.map((option, index) => (
          <OptionNavigate key={index} width={optionWidth} type={option.type} />
        ))}
      </Box>

      <Box gap="s8" marginTop="s24" flexDirection="row" paddingHorizontal="s20">
        <CardBalance
          preset="outline"
          icon="pix"
          sizeIcon={16}
          label="PIX (Hoje)"
          value={formatCurrency(report?.salesByMethod.PIX ?? 0)}
          boxProps={{ flex: 1 }}
        />
        <CardBalance
          preset="outline"
          icon="walletCheck"
          sizeIcon={16}
          label="Cartão (Hoje)"
          value={formatCurrency(report?.salesByMethod.CREDIT_CARD ?? 0)}
          boxProps={{ flex: 1 }}
        />
      </Box>

      <Box gap="s8" marginTop="s24" flexDirection="row" paddingHorizontal="s20">
      <FlatList
        data={dataWithdrawal}
        renderItem={({ item }) => <CardTransaction data={item} />}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{
          gap: 16,
          backgroundColor: colors.white,
          borderTopLeftRadius: borderRadii.secondary,
          borderTopRightRadius: borderRadii.secondary,
          padding: 16,
        }}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <Box alignItems="center" gap="s20" marginTop="s36" justifyContent="center" flex={1}>
            <Text variant="text14Regular" color="gray500">Nenhuma transação encontrada</Text>
          </Box>
        )}
        ListHeaderComponent={() => (
          <Box
            marginTop="s16"
            flexDirection="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Text variant="text18Medium">Últimas transações</Text>
            {Boolean(withdrawal?.length) && (
              <Text color="blue" variant="text14Medium">Ver todas</Text>
            )}
          </Box>
        )}
      />
    </Box>
    </Screen >
  );
}