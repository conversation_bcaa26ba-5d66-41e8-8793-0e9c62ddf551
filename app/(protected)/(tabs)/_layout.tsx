import { Icon } from "@/src/ui/components/Icon";
import { useAppTheme } from "@/src/ui/theme/useAppTheme";
import { Tabs } from "expo-router";
import React from "react";
import { Platform } from "react-native";

export default function TabLayout() {
  const { colors } = useAppTheme()
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: colors.midnightBlack,
        tabBarInactiveTintColor: colors.gray200,
        tabBarLabelStyle: {
          fontSize: 12,
          fontFamily: "DMSansRegular",
          color: colors.text,
        },
        tabBarStyle: {
          backgroundColor: colors.grayTabBar,
          paddingTop: 5,
          paddingBottom: Platform.OS === "ios" ? 10 : 0,
          height: Platform.OS === "ios" ? 90 : 100,
          borderTopWidth: 0,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "Início",
          tabBarIcon: ({ focused }) => {
            return (
              <Icon
                name={focused ? "home" : "home"}
                color={focused ? 'midnightBlack' : 'gray500'}
              />
            )
          },
        }}
      />
      <Tabs.Screen
        name="wallet" 
        options={{
          title: "Carteira",
          tabBarIcon: ({ focused }) => {
            return (
              <Icon
                name={focused ? "wallet" : "wallet"}
                color={focused ? 'midnightBlack' : 'gray500'}
              />
            )
          },
        }}
      />
      <Tabs.Screen
        name="transaction-history" 
        options={{
          title: "Transações",
          tabBarIcon: ({ focused }) => {
            return (
              <Icon
                name={focused ? "moneyRecive" : "moneyRecive"}
                color={focused ? 'midnightBlack' : 'gray500'}
              />
            )
          },
        }}
      />
    </Tabs>
  );
}